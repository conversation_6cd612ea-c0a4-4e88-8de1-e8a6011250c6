# Android APK Signing Instructions for Colony

This guide provides comprehensive instructions for creating certificates and signing APK files for the Colony Android application.

## Prerequisites

1. **Java Development Kit (JDK)** - Required for keytool and jarsigner
   ```bash
   # Check if Java is installed
   java -version
   javac -version
   
   # If not installed, install OpenJDK 11 or later
   # Ubuntu/Debian:
   sudo apt update
   sudo apt install openjdk-11-jdk
   
   # macOS (with Homebrew):
   brew install openjdk@11
   
   # Windows: Download from https://adoptium.net/
   ```

2. **Android SDK Build Tools** (Optional but recommended)
   ```bash
   # If you have Android Studio installed, build tools are usually at:
   # Windows: %LOCALAPPDATA%\Android\Sdk\build-tools\
   # macOS: ~/Library/Android/sdk/build-tools/
   # Linux: ~/Android/Sdk/build-tools/
   ```

## Step 1: Create a Signing Certificate

### Option A: Create a Self-Signed Certificate (For Testing/Development)

```bash
# Create a keystore with a self-signed certificate
keytool -genkey -v -keystore colony-release-key.keystore -alias colony-key -keyalg RSA -keysize 2048 -validity 10000

# You'll be prompted for:
# - Keystore password (remember this!)
# - Key password (can be same as keystore password)
# - Your name and organization details
```

**Example prompts and responses:**
```
Enter keystore password: [Create a strong password]
Re-enter new password: [Repeat the password]
What is your first and last name?
  [Unknown]:  Colony Developer
What is the name of your organizational unit?
  [Unknown]:  Development
What is the name of your organization?
  [Unknown]:  Colony Project
What is the name of your City or Locality?
  [Unknown]:  Your City
What is the name of your State or Province?
  [Unknown]:  Your State
What is the two-letter country code for this unit?
  [Unknown]:  US
Is CN=Colony Developer, OU=Development, O=Colony Project, L=Your City, ST=Your State, C=US correct?
  [no]:  yes

Enter key password for <colony-key>
        (RETURN if same as keystore password): [Press Enter or create different password]
```

### Option B: Create a Production Certificate (For Distribution)

For production apps, you should create a more secure certificate:

```bash
# Create a production keystore
keytool -genkey -v -keystore colony-release.keystore -alias colony-release -keyalg RSA -keysize 4096 -validity 25000 -sigalg SHA256withRSA

# Use strong passwords and real organization information
```

## Step 2: Build the Unsigned APK

First, build your Colony APK using Tauri:

```bash
# Navigate to your Colony project directory
cd /path/to/colony

# Build the Android APK
cargo tauri android build --release

# The unsigned APK will be generated at:
# src-tauri/gen/android/app/build/outputs/apk/universal/release/app-universal-release-unsigned.apk
```

## Step 3: Sign the APK

### Method A: Using jarsigner (Traditional Method)

```bash
# Navigate to the APK directory
cd src-tauri/gen/android/app/build/outputs/apk/universal/release/

# Sign the APK
jarsigner -verbose -sigalg SHA256withRSA -digestalg SHA-256 -keystore /path/to/colony-release-key.keystore app-universal-release-unsigned.apk colony-key

# You'll be prompted for the keystore password
```

### Method B: Using apksigner (Recommended)

If you have Android SDK build tools installed:

```bash
# Find your build tools version
ls ~/Android/Sdk/build-tools/  # or appropriate path for your system

# Sign using apksigner (replace 34.0.0 with your build tools version)
~/Android/Sdk/build-tools/34.0.0/apksigner sign --ks /path/to/colony-release.keystore --ks-key-alias colony-rlease --out colony-signed.apk app-universal-release-unsigned.apk

# You'll be prompted for the keystore password
```

## Step 4: Align the APK (Optional but Recommended)

APK alignment optimizes the APK for better performance:

```bash
# Using zipalign from Android SDK build tools
~/Android/Sdk/build-tools/34.0.0/zipalign -v 4 colony-signed.apk colony-signed-aligned.apk

# If you used jarsigner, align after signing:
~/Android/Sdk/build-tools/34.0.0/zipalign -v 4 app-universal-release-unsigned.apk colony-aligned.apk
```

## Step 5: Verify the Signature

Verify that your APK is properly signed:

```bash
# Verify using jarsigner
jarsigner -verify -verbose -certs colony-signed.apk

# Or verify using apksigner
~/Android/Sdk/build-tools/34.0.0/apksigner verify colony-signed.apk

# Check APK details
keytool -printcert -jarfile colony-signed.apk
```

## Step 6: Install the Signed APK

### On Your Device:

1. **Enable Developer Options:**
   - Go to Settings > About Phone
   - Tap "Build Number" 7 times
   - Go back to Settings > Developer Options
   - Enable "USB Debugging"

2. **Enable Unknown Sources:**
   - Go to Settings > Security (or Privacy)
   - Enable "Unknown Sources" or "Install from Unknown Sources"
   - Or for newer Android: Settings > Apps > Special Access > Install Unknown Apps

3. **Install via ADB:**
   ```bash
   # Connect your device via USB
   adb devices  # Verify device is connected
   adb install colony-signed.apk
   ```

4. **Install via File Transfer:**
   - Copy the signed APK to your device
   - Use a file manager to navigate to the APK
   - Tap the APK file and follow installation prompts

## Automated Signing Script

Create a script to automate the signing process:

```bash
#!/bin/bash
# sign-colony-apk.sh

set -e

KEYSTORE_PATH="./colony-release-key.keystore"
KEY_ALIAS="colony-key"
APK_PATH="src-tauri/gen/android/app/build/outputs/apk/universal/release/app-universal-release-unsigned.apk"
OUTPUT_APK="colony-signed.apk"

echo "Building Colony APK..."
cargo tauri android build --release

echo "Signing APK..."
jarsigner -verbose -sigalg SHA256withRSA -digestalg SHA-256 -keystore "$KEYSTORE_PATH" "$APK_PATH" "$KEY_ALIAS"

echo "Copying signed APK..."
cp "$APK_PATH" "$OUTPUT_APK"

echo "Verifying signature..."
jarsigner -verify -verbose "$OUTPUT_APK"

echo "APK signed successfully: $OUTPUT_APK"
echo "You can now install this APK on Android devices."
```

Make the script executable:
```bash
chmod +x sign-colony-apk.sh
./sign-colony-apk.sh
```

## Security Best Practices

1. **Protect Your Keystore:**
   - Store keystore files securely
   - Use strong passwords
   - Keep backups in secure locations
   - Never commit keystores to version control

2. **Certificate Validity:**
   - Use long validity periods (25+ years for production)
   - Keep certificate information consistent

3. **Build Security:**
   - Always use release builds for signing
   - Verify APK contents before signing
   - Test signed APKs thoroughly

## Troubleshooting

### Common Issues:

1. **"Certificate fingerprints do not match"**
   - You're trying to update an app with a different certificate
   - Uninstall the old app first

2. **"App not installed"**
   - Enable "Unknown Sources"
   - Check available storage space
   - Verify APK is properly signed

3. **"Parse error"**
   - APK may be corrupted
   - Rebuild and re-sign the APK

4. **Permission denied during installation**
   - Check that USB debugging is enabled
   - Verify ADB connection: `adb devices`

### Verification Commands:

```bash
# Check APK signature details
keytool -printcert -jarfile colony-signed.apk

# List APK contents
unzip -l colony-signed.apk

# Check APK alignment
~/Android/Sdk/build-tools/34.0.0/aapt dump badging colony-signed.apk
```

## Next Steps

After successfully signing and installing your APK:

1. Test all app functionality on the device
2. Verify file downloads work in the app data directory
3. Test the hamburger menu visibility improvements
4. Confirm all Android-specific UI changes work correctly

Your signed APK can now be installed on any Android device with "Unknown Sources" enabled!
